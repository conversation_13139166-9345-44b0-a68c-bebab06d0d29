
import React, { useEffect, useRef, useState } from "react";
import { cn } from "@/lib/utils";

interface LegacyCardProps {
  icon: React.ReactNode;
  number: string;
  title: string;
  subtitle: string;
  bgColor: string;
  index: number;
}

const LegacyCard = ({ icon, number, title, subtitle, bgColor, index }: LegacyCardProps) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in");
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1 }
    );

    if (cardRef.current) {
      observer.observe(cardRef.current);
    }

    return () => {
      if (cardRef.current) {
        observer.unobserve(cardRef.current);
      }
    };
  }, []);

  return (
    <div
      ref={cardRef}
      className={cn(
        "legacy-card opacity-0 relative overflow-hidden rounded-2xl transition-all duration-500 hover:scale-105 group cursor-pointer",
        bgColor
      )}
      style={{ animationDelay: `${0.15 * index}s` }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-4 right-4 text-6xl opacity-20">{icon}</div>
      </div>

      {/* Content */}
      <div className="relative p-8 h-full flex flex-col justify-between min-h-[200px]">
        <div className="flex items-start justify-between mb-4">
          <div className="text-3xl">{icon}</div>
          {number && (
            <div className="text-right">
              <div className="text-3xl font-bold text-black">{number}</div>
            </div>
          )}
        </div>

        <div className="space-y-2">
          <h3 className="text-lg font-bold text-black leading-tight">{title}</h3>
          {subtitle && (
            <p className="text-sm text-black/80 leading-relaxed">{subtitle}</p>
          )}
        </div>

        {/* Hover Effect */}
        <div className={cn(
          "absolute inset-0 bg-black/5 transition-opacity duration-300",
          isHovered ? "opacity-100" : "opacity-0"
        )} />
      </div>
    </div>
  );
};

const Features = () => {
  const sectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const elements = entry.target.querySelectorAll(".fade-in-element");
            elements.forEach((el, index) => {
              setTimeout(() => {
                el.classList.add("animate-fade-in");
              }, index * 150);
            });
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  return (
    <section className="py-20 relative bg-white overflow-hidden" id="features" ref={sectionRef}>
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50/50 to-white"></div>
      <div className="absolute top-0 right-0 w-96 h-96 bg-pulse-500/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>

      <div className="relative container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-black text-white text-sm font-medium mb-6 opacity-0 fade-in-element">
            40+ YEARS EXCELLENCE
          </div>
          <h2 className="text-5xl md:text-6xl font-bold text-black mb-6 opacity-0 fade-in-element">
            Our Legacy
          </h2>
          <div className="w-24 h-1 bg-black mx-auto opacity-0 fade-in-element"></div>
        </div>

        {/* Company Description */}
        <div className="max-w-4xl mx-auto mb-20 opacity-0 fade-in-element">
          <p className="text-xl text-black leading-relaxed text-center">
            Atandra Energy Pvt. Ltd., headquartered in Chennai, draws upon a rich foundation of more than 40+ years of
            expertise in the realm of <span className="font-semibold">Power & Energy Management</span>.
          </p>
        </div>

        {/* Achievement Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          <LegacyCard
            icon={<span>🏆</span>}
            number=""
            title="INDIA'S NO.1"
            subtitle="MANUFACTURER OF SERVO STABILIZERS"
            bgColor="bg-gradient-to-br from-orange-100 to-orange-200"
            index={0}
          />
          <LegacyCard
            icon={<span>📊</span>}
            number="100+"
            title="SERVICE CENTRES"
            subtitle=""
            bgColor="bg-gradient-to-br from-blue-100 to-blue-200"
            index={1}
          />
          <LegacyCard
            icon={<span>🏢</span>}
            number=""
            title="PREFERRED"
            subtitle="SUPPLIER OF LARGE CORPORATES & OEMS"
            bgColor="bg-gradient-to-br from-green-100 to-green-200"
            index={2}
          />
          <LegacyCard
            icon={<span>🎖️</span>}
            number=""
            title="CERTIFIED"
            subtitle="PRODUCTS"
            bgColor="bg-gradient-to-br from-purple-100 to-purple-200"
            index={3}
          />
          <LegacyCard
            icon={<span>⭐</span>}
            number="40+"
            title="YEARS EXPERIENCE"
            subtitle=""
            bgColor="bg-gradient-to-br from-yellow-100 to-yellow-200"
            index={4}
          />
          <LegacyCard
            icon={<span>🌟</span>}
            number=""
            title="CERTIFIED QUALITY"
            subtitle="STANDARDS"
            bgColor="bg-gradient-to-br from-teal-100 to-teal-200"
            index={5}
          />
        </div>

        {/* Sustainability Commitment Section */}
        <div className="max-w-5xl mx-auto">
          <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl p-12 text-black opacity-0 fade-in-element">
            <div className="text-center">
              <h3 className="text-3xl font-bold mb-6">Sustainability Commitment</h3>
              <p className="text-lg leading-relaxed">
                State-of-the-art facilities empower us to address the requirements of Indian Industries comprehensively,
                effectively & efficiently, ensuring they derive maximum benefits from the best-in-class conditioning & energy
                management solutions we provide.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;
